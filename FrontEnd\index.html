
<!DOCTYPE html>
<html lang="ar">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Document</title>
    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
    <link href="https://fonts.googleapis.com/css2?family=Tajawal:wght@300&family=Cairo:wght@300&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/noUiSlider/15.5.0/nouislider.min.css">
    <script src="https://cdnjs.cloudflare.com/ajax/libs/noUiSlider/15.5.0/nouislider.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/sweetalert2@11"></script>
    <script src="https://code.jquery.com/jquery-3.7.1.js"></script>
  <script src="https://code.jquery.com/ui/1.13.3/jquery-ui.js"></script>
    <style>
        ::-webkit-scrollbar {
            display: none;
        }

        * {
            margin: 0;
            font-family: 'Tajawal', sans-serif;
        }

        .items-container {
            position: relative;
            width: 100%;
            height: 85%;
            top: 60px;
            left: 20px;
            margin: 10px;
            overflow-x: hidden;
        }

        .item {
            position: relative;
            display: inline-block;
            background-color: #1c1c1c;
            padding-top: 10px;
            border-radius: 30px;
            left: 0vh;
            width: 30%;
            height: 210px;
            text-align: center;
            overflow: hidden;
            margin: 10px;
            box-shadow: #FF0000 0px 0px 6px 1px;
        }

        .buy {
            position: relative;
            color: #ffffff;
            background-color: #22242b;
            width: 300px;
            left: 35px;
            top: 120px;
            border-radius: 10vh;
            height: 3vh;
            text-align: center;
            font-size: 20px;
            box-shadow: #FF0000 0px 0px 6px 1px;
            cursor: pointer;
        }

        .sswwwebac {
            position: relative;
            background-color: #ffffff;
            width: 400px;
            height: 1px;
            top: 0px;
        }

        .name, .price {
            position: relative;
            color: #fff;
            top: 30px;
        }

        .WopenShopBackgraunw {
            position: relative;
            background-color: #22242b;
            width: 115vh;
            height: 70vh;
            top: -300px;
            left: 50%;
            transform: translate(-50%, 50%);
            border-radius: 5.2ch;
            box-shadow: #FF0000 0px 0px 6px 1px;
            cursor: all-scroll;
            
        }

        .ServerLogo {
            position: absolute;
            width: 4vw;
            top: 1vh;
            left: 1vh;
            border-radius: 20vw;
        }

        .ServerName {
            position: absolute;
            width: 10vw;
            top: 3vh;
            left: 10vh;
            font-size: 2vh;
            color: #fff;
            font-family: 'Cairo', sans-serif;
        }

        .Moneyback {
            position: relative;
            background-color: #22242b;
            width: 20vh;
            height: 3vh;
            top: 4px;
            left: 90%;
            transform: translate(-50%, 50%);
            border-radius: 2.2ch;
            box-shadow: rgb(255, 255, 255) 0px 0px 6px 1px;
        }

        .Moneytext {
            position: relative;
            color: #73ff00;
            left: 14vh;
            top: 3px;
        }

        .Money {
            position: relative;
            color: #ffffff;
            left: 2vh;
            top: -17px;
            white-space: nowrap;
            overflow: hidden;
            text-overflow: ellipsis;
            max-width: 100px;
        }

        .script_container {
            position: relative;
            display: inline-block;
            margin: 1%;
            top: 10px;
            left: 10px;
            width: 22vh;
            height: 18vh;
            background-color: #FF0000;
            border-radius: 2.2ch;
        }

        .border {
            position: absolute;
            top: 53%;
            left: 3%;
            background: linear-gradient(to left, rgba(255, 255, 255, 0) 0%, rgb(246, 246, 246) 50%, rgba(255, 255, 255, 0) 100%);
            width: 90%;
            height: 1px;
        }

        .img {
            position: absolute;
            left: 50%;
            top: 5%;
            width: 10vh;
            transform: scale(1.1) translate(-50%, 50%);
            padding: 5px 10px;
            border-radius: 2.2ch;
        }

        .script_item_info {
            position: relative;
            top: 55%;
            text-align: center;
            color: white;
        }

        .script_item_info a {
            color: rgb(25, 247, 0);
        }

        .script_item_buy {
            position: relative;
            top: 59%;
            left: 50px;
            background-color: rgba(20, 20, 29, 0.868);
            width: 12vh;
            height: 1vh;
            padding: 10px 7px;
            border-radius: 1.2ch;
            text-align: center;
            color: white;
            line-height: 10px;
            cursor: pointer;
            transition: 0.5s all;
        }

        .script_item_buy:hover {
            transform: scale(0.93);
        }

        .swal2-popup {
    background: white !important;
}

.closeBtn{
    position: absolute;
    background-color: rgba(20, 20, 29, 0.868);
    top: 72vh;
    left: 54vh;
    border-radius: 10px;
    width: 100px;
    height: 50px;
    color: #fff;
    font-size: 18px;
    text-align: center;
    line-height: 50px; 
    border-bottom-left-radius: 10px;
    border-top-right-radius: 0px;
    border-top-left-radius: 0px;
    border-bottom-right-radius: 10px;
    box-shadow: #6a516e 0px 0px 6px 0px;
    cursor: pointer;


}

.item .buy {

    transition: transform 0.3s ease-in-out;

}

.item .buy:hover {
    transform: scale(0.9);
}




    </style>
</head>
<body>

    <div class="WopenShopBackgraunw" id="weaponShop" style="display:none">
        <img src="https://cdn.discordapp.com/attachments/1262863548414165115/1265219830928375890/512.gif?ex=66a0b74a&is=669f65ca&hm=acc95e004d01a400c394c54d232db79c7c3fae7afae3538b0c663fd0511fec1b&" id="Server-Logo" class="ServerLogo">
        <div class="ServerName">Greats Store</div>
        <div class="closeBtn">اغلاق القائمة</div>
        <div class="Moneyback">
            <div class="Moneytext">: الرصيد</div>
            <div class="Money" id="playerMoney">0$</div>
        </div>
        <div class="items-container">
        </div>
    </div>

    <script>
  window.addEventListener('message', function(event) {
    if (event.data.action === 'openShop') {
        $('#weaponShop').show();
        $('.items-container').empty();

        $('#playerMoney').text(event.data.playerMoney + '$');
        document.querySelector('.ServerName').innerText = event.data.serverName;
        document.getElementById('Server-Logo').src = event.data.ServerLogo;
        event.data.shop.weapons.forEach(weapon => {
            $('.items-container').append(`
                <div class="item">
                    <img src="img/${weapon.image}" class="img">
                    <div class="border"></div>
                    <div class="script_item_info">${weapon.name}</div>
                    <div class="buy" data-weapon="${weapon.name}" data-price="${weapon.price}">شراء</div>
                </div>
            `);
        });

        $(document).ready(function() {
            $('.buy').click(async function() {
                const { value: Ammo } = await Swal.fire({
                    title: "عدد الرصاص",
                    html: `
                    <div style="margin: 20px; border-radius: 20px;">
                        <div id="slider" style="margin-bottom: 20px;"></div>
                        <input type="number" id="sliderValue" readonly style="width: 100%; border-radius: 20px;">
                    </div>
                    `,
                    didOpen: () => {
                        const slider = document.getElementById('slider');
                        const sliderValue = document.getElementById('sliderValue');

                        noUiSlider.create(slider, {
                            start: 50, 
                            connect: [true, false],
                            range: {
                                'min': 0,
                                'max': 500
                            },
                            step: 1
                        });

                        slider.noUiSlider.on('update', (values, handle) => {
                            sliderValue.value = Math.round(values[handle]); 
                        });
                    },
                    preConfirm: () => {
                        return slider.noUiSlider.get(); 
                    },
                    showCancelButton: true,
                    confirmButtonText: 'تأكيد',
                    cancelButtonText: 'إلغاء'
                });

                if (Ammo !== undefined) {
 Swal.fire({
        title: `<i class="fa fa-check-circle" style="color: green;"></i> تم شراء ${Math.round(Ammo)} طلقة بنجاح`, 
        html: `<i class="fa fa-check-circle" style="color: green; font-size: 24px;"></i> تم شراء ${Math.round(Ammo)} طلقة بنجاح`, 
        icon: 'success'
    }); 
                    $.post('http://CB_GunShop/BuyGun', JSON.stringify({
                        Count: Math.round(Ammo), 
                        weapon: $(this).data('weapon'),
                        price: $(this).data('price')
                    }));
                }
            });
            $('.buy').on('mouseenter', function() {
                const price = $(this).data('price');
                $(this).text(`${price}$`);
            }).on('mouseleave', function() {
                $(this).text('شراء');
            });
        });
    }
});

$(document).on('click', '.closeBtn', function() {
    $('#weaponShop').hide();
    $.post('http://CB_GunShop/closeMenu', JSON.stringify({}));
});    

$( function() {
    $( ".WopenShopBackgraunw" ).draggable();
  } );
    </script>

</body>
</html>
