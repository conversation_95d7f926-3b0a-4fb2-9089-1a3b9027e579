local Tunnel = module("vrp", "lib/Tunnel")
local Proxy = module("vrp", "lib/Proxy")
vRP = Proxy.getInterface("vRP")
vRPclient = Tunnel.getInterface("vRP", "weaponshop")

RegisterServerEvent("Ag:OpenPanel", function (Shop)
    local player = source
    local user_id = vRP.getUserId({player})
    local shopPermission = Shop.Permission
    local hasPermission = vRP.hasPermission({user_id, shopPermission})
    
    if hasPermission then
        if player and player ~= nil then
            local playerMoney = vRP.getMoney({user_id}) + vRP.getBankMoney({user_id})
            TriggerClientEvent('Ag:Ui', player, playerMoney, Shop)
        end
    else
        vRPclient.notify(player,{"لايوجد لديك صلاحية لاستخدام هذا المتجر"})
    end
end)

RegisterServerEvent('weaponshop:buyWeapon')
AddEventHandler('weaponshop:buyWeapon', function(weapon, price, Ammo)
    local user_id = vRP.getUserId({source})
    local player = vRP.getUserSource({user_id})
    if type(Ammo) == "string" then
        Ammo = tonumber(Ammo)
    end
    if type(price) == "string" then
        price = tonumber(price)
    end
    local FinalPrice = 100 * Ammo + price
    if vRP.tryFullPayment({user_id, FinalPrice}) then
        vRPclient.giveWeapons(player, {{[weapon] = {ammo = tonumber(Ammo)}}}, false)
        vRPclient.notify(player, {"تم شراء السلاح بنجاح"})
    else
        vRPclient.notify(player, {"لا يوجد لديك مبلغ كافي لشراء السلاح !!"})
    end
end)