

RegisterFontFile(Config.Font)
fontId = RegisterFontId(Config.Font)

RegisterNetEvent("Ag:Ui", function (Money,data)
    SetNuiFocus(true, true)
    SendNUIMessage({ ServerLogo = Config.Serverlogo, serverName = Config.servername,  playerMoney = Money , action = 'openShop', shop = data  })
end)

Citizen.CreateThread(function()
    for _, shop in ipairs(Config.Shops) do
        local blip = AddBlipForCoord(shop.coords.x, shop.coords.y, shop.coords.z)
        SetBlipSprite(blip, 110)
        SetBlipDisplay(blip, 4)
        SetBlipScale(blip, 0.9)
        SetBlipColour(blip, 1)
        SetBlipAsShortRange(blip, true)
        BeginTextCommandSetBlipName("STRING")
        AddTextComponentString(shop.name)
        EndTextCommandSetBlipName(blip)
    end

    while true do
        Citizen.Wait(0)
        local playerPed = PlayerPedId()
        local coords = GetEntityCoords(playerPed)

        for _, shop in ipairs(Config.Shops) do
            local distance = GetDistanceBetweenCoords(coords, shop.coords.x, shop.coords.y, shop.coords.z, true)
            if distance < 10 then
                DrawText3D(shop.coords.x, shop.coords.y, shop.coords.z + 0.0, Config.TextMarker)

                if not HasStreamedTextureDictLoaded("AgMarkert") then
                    RequestStreamedTextureDict("AgMarkert", true)
                    while not HasStreamedTextureDictLoaded("AgMarkert") do
                        Wait(1)
                    end
                end


                DrawMarker(9, shop.coords.x, shop.coords.y, shop.coords.z + 0.1, 0.0, 0.0, 0.0, 0.0, 90.0, 0.5, 0.7, 0.7, 1.1, 255, 255, 255, 255, false, false, 2, true, "AgMarkert", "konar", false)

                if IsControlJustReleased(0, 38) then
                    TriggerServerEvent("Ag:OpenPanel", shop)
                end
            end
        end
    end
end)



RegisterNUICallback("BuyGun", function (data)
    local weapon = data.weapon
    local price = data.price
    local Ammo = data.Count
TriggerServerEvent('weaponshop:buyWeapon', weapon, price, Ammo)
end)

RegisterNUICallback('closeShop', function(data, cb)
    SetNuiFocus(false, false)
    SendNUIMessage({ action = 'closeShop' })
    cb('ok')
end)

function DrawText3D(x, y, z, text)
    local onScreen, _x, _y = World3dToScreen2d(x, y, z)
    local scale = 0.35
    if onScreen then
        SetTextScale(scale, scale)
        SetTextFont(fontId)
        SetTextProportional(1)
        SetTextColour(255, 0, 0, 215)
        SetTextEntry("STRING")
        SetTextCentre(1)
        AddTextComponentString(text)
        DrawText(_x, _y)
    end
end

RegisterNUICallback('closeMenu', function(data, cb)
    SetNuiFocus(false, false)
    cb('ok')
end)


--[[
░█████╗░░██████╗░  ████████╗███████╗░█████╗░███╗░░░███╗
██╔══██╗██╔════╝░  ╚══██╔══╝██╔════╝██╔══██╗████╗░████║
███████║██║░░██╗░  ░░░██║░░░█████╗░░███████║██╔████╔██║
██╔══██║██║░░╚██╗  ░░░██║░░░██╔══╝░░██╔══██║██║╚██╔╝██║
██║░░██║╚██████╔╝  ░░░██║░░░███████╗██║░░██║██║░╚═╝░██║
╚═╝░░╚═╝░╚═════╝░  ░░░╚═╝░░░╚══════╝╚═╝░░╚═╝╚═╝░░░░░╚═╝
	=========================================
	
	Made By : .q3v
    Discord : https://discord.gg/ag1
	
	♥ شكرا لثقتك بنا ♥
]]